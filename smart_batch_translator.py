#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart Batch Translator
Automatically optimizes batch size based on content and limits
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv

load_dotenv()

class SmartBatchTranslator:
    def __init__(self):
        # API configuration
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key or api_key == 'your_google_api_key_here':
            raise ValueError("Please set your Google API key in the .env file")
        
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.0-flash-lite')
        self.model = genai.GenerativeModel(self.model_name)
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # Token limits (conservative estimates)
        self.max_input_tokens = 30000  # Leave buffer for safety
        self.max_output_tokens = 8000
        self.avg_thai_tokens_per_char = 0.8  # Thai characters to tokens ratio
        self.avg_english_tokens_per_char = 0.25  # English characters to tokens ratio
        
        print(f"🧠 Smart Batch Translator")
        print(f"   🤖 Model: {self.model_name}")
        print(f"   📊 Max input tokens: {self.max_input_tokens:,}")
        
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        return int(len(text) * self.avg_thai_tokens_per_char)
    
    def calculate_optimal_batch_size(self, sms_contents: List[str]) -> int:
        """Calculate optimal batch size based on content"""
        if not sms_contents:
            return 50
        
        # Sample first 10 messages to estimate average length
        sample_size = min(10, len(sms_contents))
        sample_messages = sms_contents[:sample_size]
        
        # Calculate average tokens per message
        total_tokens = sum(self.estimate_tokens(msg) for msg in sample_messages)
        avg_tokens_per_message = total_tokens / sample_size
        
        # Add prompt overhead (approximately 200 tokens)
        prompt_overhead = 200
        
        # Calculate max messages per batch
        available_tokens = self.max_input_tokens - prompt_overhead
        max_messages_per_batch = int(available_tokens / avg_tokens_per_message)
        
        # Ensure reasonable bounds
        optimal_batch_size = max(5, min(max_messages_per_batch, 100))
        
        print(f"📊 Batch Size Analysis:")
        print(f"   Average tokens/message: {avg_tokens_per_message:.1f}")
        print(f"   Optimal batch size: {optimal_batch_size}")
        print(f"   Estimated token usage: {optimal_batch_size * avg_tokens_per_message + prompt_overhead:.0f}/{self.max_input_tokens}")
        
        return optimal_batch_size
    
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_smart_batch(self, batch_contents: List[str]) -> Dict[str, str]:
        """Translate batch with smart error handling"""
        thai_contents = [content for content in batch_contents if self.is_thai_text(content)]
        
        if not thai_contents:
            return {}
        
        # Estimate total tokens for this batch
        total_tokens = sum(self.estimate_tokens(msg) for msg in thai_contents) + 200
        
        if total_tokens > self.max_input_tokens:
            print(f"⚠️  Batch too large ({total_tokens} tokens), splitting...")
            # Split batch in half and process separately
            mid = len(thai_contents) // 2
            batch1 = thai_contents[:mid]
            batch2 = thai_contents[mid:]
            
            result1 = self.translate_smart_batch(batch1) if batch1 else {}
            time.sleep(0.1)  # Small delay between splits
            result2 = self.translate_smart_batch(batch2) if batch2 else {}
            
            result1.update(result2)
            return result1
        
        try:
            # Create optimized prompt
            numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_contents)])
            
            prompt = f"""Translate these {len(thai_contents)} Thai SMS messages to English.
Keep the same numbered format. Maintain casual tone and preserve special characters:

{numbered_texts}"""
            
            print(f"🔄 Translating batch of {len(thai_contents)} messages ({total_tokens} tokens)...")
            
            response = self.model.generate_content(prompt)
            translated_lines = response.text.strip().split('\n')
            
            # Parse responses
            translations = {}
            for line in translated_lines:
                if '. ' in line and line.strip():
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_contents):
                            original = thai_contents[num]
                            translations[original] = translation.strip()
                    except (ValueError, IndexError):
                        continue
            
            success_rate = len(translations) / len(thai_contents) * 100
            print(f"✅ Translated {len(translations)}/{len(thai_contents)} messages ({success_rate:.1f}%)")
            
            return translations
            
        except Exception as e:
            print(f"❌ Translation error: {e}")
            
            # If batch is large, try splitting it
            if len(thai_contents) > 10:
                print("🔄 Retrying with smaller batches...")
                mid = len(thai_contents) // 2
                batch1 = thai_contents[:mid]
                batch2 = thai_contents[mid:]
                
                result1 = self.translate_smart_batch(batch1) if batch1 else {}
                time.sleep(1)  # Longer delay after error
                result2 = self.translate_smart_batch(batch2) if batch2 else {}
                
                result1.update(result2)
                return result1
            
            return {}
    
    def translate_file_smart(self, input_file: str) -> bool:
        """Translate file with smart batching"""
        try:
            print(f"\n📱 Smart processing: {input_file}")
            
            # Read JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract SMS records
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print(f"❌ No SMS records found")
                return False
            
            # Collect Thai contents
            thai_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content and self.is_thai_text(content):
                    thai_contents.append(content)
            
            if not thai_contents:
                print(f"❌ No Thai SMS content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            
            # Calculate optimal batch size
            optimal_batch_size = self.calculate_optimal_batch_size(thai_contents)
            
            # Process with smart batching
            all_translations = {}
            total_batches = (len(thai_contents) - 1) // optimal_batch_size + 1
            start_time = time.time()
            
            for i in range(0, len(thai_contents), optimal_batch_size):
                batch = thai_contents[i:i+optimal_batch_size]
                batch_num = i // optimal_batch_size + 1
                
                print(f"\n📦 Batch {batch_num}/{total_batches}")
                batch_translations = self.translate_smart_batch(batch)
                all_translations.update(batch_translations)
                
                # Progress update
                progress = len(all_translations) / len(thai_contents) * 100
                elapsed = time.time() - start_time
                rate = len(all_translations) / elapsed if elapsed > 0 else 0
                
                print(f"📈 Progress: {progress:.1f}% | Rate: {rate:.1f} SMS/sec")
                
                # Small delay to avoid rate limits
                time.sleep(0.1)
            
            # Apply translations
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save result
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_smart_translated.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Final stats
            total_time = time.time() - start_time
            final_rate = len(all_translations) / total_time if total_time > 0 else 0
            success_rate = len(all_translations) / len(thai_contents) * 100
            
            print(f"\n🎉 Smart translation completed!")
            print(f"✅ Success rate: {success_rate:.1f}% ({len(all_translations)}/{len(thai_contents)})")
            print(f"⏱️  Total time: {total_time:.1f} seconds")
            print(f"🚀 Average rate: {final_rate:.1f} SMS/second")
            print(f"📄 Output: {output_filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    print("🧠 Smart Batch Translator")
    print("=" * 50)
    print("Automatically optimizes batch size based on content!")
    print()
    
    # List files
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print("📋 Available files:")
    for i, file in enumerate(json_files, 1):
        size_kb = os.path.getsize(file) / 1024
        print(f"   {i}. {file} ({size_kb:.0f}KB)")
    
    try:
        choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[choice]
        
        translator = SmartBatchTranslator()
        success = translator.translate_file_smart(selected_file)
        
        if success:
            print(f"\n🎉 Translation completed successfully!")
        else:
            print(f"\n❌ Translation failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
