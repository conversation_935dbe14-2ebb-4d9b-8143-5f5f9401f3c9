#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Speed Optimizer for Translation
Automatically adjusts settings for optimal speed
"""

import os
import shutil
from dotenv import load_dotenv

def optimize_for_speed():
    """Optimize configuration for maximum speed"""
    print("🚀 Speed Optimizer")
    print("=" * 40)
    
    # Backup current .env
    if os.path.exists('.env'):
        shutil.copy('.env', '.env.backup')
        print("💾 Backed up current .env to .env.backup")
    
    # Speed configurations
    configs = {
        "1": {
            "name": "🐌 Safe Mode",
            "batch_size": 8,
            "delay": 0.8,
            "description": "Conservative settings, lowest risk"
        },
        "2": {
            "name": "🚀 Fast Mode", 
            "batch_size": 15,
            "delay": 0.4,
            "description": "Balanced speed and reliability"
        },
        "3": {
            "name": "⚡ Turbo Mode",
            "batch_size": 25,
            "delay": 0.2,
            "description": "Maximum speed, higher risk of rate limits"
        }
    }
    
    print("\nChoose optimization level:")
    for key, config in configs.items():
        print(f"{key}. {config['name']}")
        print(f"   Batch: {config['batch_size']}, Delay: {config['delay']}s")
        print(f"   {config['description']}")
        print()
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice not in configs:
        print("❌ Invalid choice")
        return
    
    config = configs[choice]
    
    # Read current API key
    load_dotenv()
    api_key = os.getenv('GOOGLE_API_KEY', 'your_google_api_key_here')
    model_name = os.getenv('MODEL_NAME', 'gemini-2.5-flash-preview-05-20')
    
    # Write optimized .env
    env_content = f"""# Optimized for Speed - {config['name']}
GOOGLE_API_KEY={api_key}
GOOGLE_API_URL=https://generativelanguage.googleapis.com
MODEL_NAME={model_name}

# Speed Optimizations
BATCH_SIZE={config['batch_size']}
RATE_LIMIT_DELAY={config['delay']}
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"✅ Applied {config['name']} configuration")
    print(f"📦 Batch size: {config['batch_size']}")
    print(f"⏱️  Delay: {config['delay']}s")
    
    # Calculate estimated speed improvement
    old_batch = 5
    old_delay = 1.0
    old_time_per_batch = old_delay + 2  # 2s for API call
    new_time_per_batch = config['delay'] + 2
    
    old_messages_per_minute = (60 / old_time_per_batch) * old_batch
    new_messages_per_minute = (60 / new_time_per_batch) * config['batch_size']
    
    speedup = new_messages_per_minute / old_messages_per_minute
    
    print(f"\n📈 Estimated Performance:")
    print(f"   Old: ~{old_messages_per_minute:.0f} messages/minute")
    print(f"   New: ~{new_messages_per_minute:.0f} messages/minute")
    print(f"   Speedup: {speedup:.1f}x faster")
    
    print(f"\n💡 Tips for {config['name']}:")
    if choice == "1":
        print("   - Most reliable, good for large files")
        print("   - Use if you encounter rate limit errors")
    elif choice == "2":
        print("   - Good balance of speed and stability")
        print("   - Recommended for most users")
    else:
        print("   - Maximum speed but watch for rate limits")
        print("   - If errors occur, switch to Fast Mode")
    
    print(f"\n🔄 To restore original settings:")
    print(f"   mv .env.backup .env")

def estimate_translation_time():
    """Estimate translation time for files"""
    load_dotenv()
    
    batch_size = int(os.getenv('BATCH_SIZE', '5'))
    delay = float(os.getenv('RATE_LIMIT_DELAY', '1.0'))
    
    # Estimate API call time (varies by model and batch size)
    api_time = 1.5 + (batch_size * 0.1)  # Base time + per message
    time_per_batch = delay + api_time
    messages_per_minute = (60 / time_per_batch) * batch_size
    
    print(f"\n⏱️  Current Speed Estimate:")
    print(f"   Batch size: {batch_size}")
    print(f"   Delay: {delay}s")
    print(f"   ~{messages_per_minute:.0f} messages/minute")
    
    # Estimate for each file
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and not f.endswith('_translated.json')]
    
    print(f"\n📊 Estimated translation times:")
    for file in json_files:
        size_kb = os.path.getsize(file) / 1024
        # Rough estimate: 1KB ≈ 2-3 SMS messages
        estimated_messages = size_kb * 2.5
        estimated_minutes = estimated_messages / messages_per_minute
        
        if estimated_minutes < 60:
            time_str = f"{estimated_minutes:.0f} minutes"
        else:
            hours = estimated_minutes / 60
            time_str = f"{hours:.1f} hours"
        
        print(f"   {file} ({size_kb:.0f}KB): ~{time_str}")

def main():
    print("Choose option:")
    print("1. 🚀 Optimize for speed")
    print("2. ⏱️  Estimate translation times")
    print("3. 🔄 Restore backup")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        optimize_for_speed()
    elif choice == "2":
        estimate_translation_time()
    elif choice == "3":
        if os.path.exists('.env.backup'):
            shutil.copy('.env.backup', '.env')
            print("✅ Restored .env from backup")
        else:
            print("❌ No backup found")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
