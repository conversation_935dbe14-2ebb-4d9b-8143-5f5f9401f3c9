#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON Thai to English Translator using Google Gemini
Translates Thai text in JSON files to English while preserving structure
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time

class JSONThaiTranslatorGemini:
    def __init__(self, api_key: str = None):
        """
        Initialize the translator with Google Gemini API
        
        Args:
            api_key: Google AI API key. If None, will try to get from environment variable GOOGLE_API_KEY
        """
        if api_key is None:
            api_key = os.getenv('GOOGLE_API_KEY')
            if not api_key:
                raise ValueError("Please provide Google API key or set GOOGLE_API_KEY environment variable")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')  # Thai Unicode range
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_text(self, text: str) -> str:
        """Translate Thai text to English using Gemini"""
        if not self.is_thai_text(text):
            return text
            
        try:
            # Add delay to avoid rate limiting
            time.sleep(0.5)
            
            prompt = f"""Please translate the following Thai text to English. 
            Only return the English translation, no explanations or additional text:
            
            {text}"""
            
            response = self.model.generate_content(prompt)
            translated_text = response.text.strip()
            
            print(f"Translated: '{text[:50]}...' -> '{translated_text[:50]}...'")
            return translated_text
            
        except Exception as e:
            print(f"Translation error for text '{text[:50]}...': {e}")
            return text  # Return original text if translation fails
    
    def translate_batch_texts(self, texts: List[str]) -> List[str]:
        """Translate multiple Thai texts in a single API call for efficiency"""
        thai_texts = [text for text in texts if self.is_thai_text(text)]
        
        if not thai_texts:
            return texts
        
        try:
            time.sleep(0.5)
            
            # Create batch translation prompt
            numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_texts)])
            
            prompt = f"""Please translate the following Thai texts to English. 
            Return only the English translations in the same numbered format, no explanations:
            
            {numbered_texts}"""
            
            response = self.model.generate_content(prompt)
            translated_lines = response.text.strip().split('\n')
            
            # Parse the numbered responses
            translated_dict = {}
            for line in translated_lines:
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_texts):
                            translated_dict[thai_texts[num]] = translation.strip()
                    except:
                        continue
            
            # Replace texts with translations
            result = []
            for text in texts:
                if text in translated_dict:
                    result.append(translated_dict[text])
                    print(f"Batch translated: '{text[:30]}...' -> '{translated_dict[text][:30]}...'")
                else:
                    result.append(text)
            
            return result
            
        except Exception as e:
            print(f"Batch translation error: {e}")
            # Fallback to individual translation
            return [self.translate_text(text) for text in texts]
    
    def extract_thai_texts(self, obj: Any, path: str = "") -> List[tuple]:
        """Extract all Thai texts from JSON structure with their paths"""
        thai_texts = []
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                thai_texts.extend(self.extract_thai_texts(value, new_path))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_path = f"{path}[{i}]"
                thai_texts.extend(self.extract_thai_texts(item, new_path))
        elif isinstance(obj, str) and self.is_thai_text(obj):
            thai_texts.append((path, obj))
        
        return thai_texts
    
    def apply_translations(self, obj: Any, translations: Dict[str, str]) -> Any:
        """Apply translations to JSON structure"""
        if isinstance(obj, dict):
            translated_dict = {}
            for key, value in obj.items():
                translated_dict[key] = self.apply_translations(value, translations)
            return translated_dict
        elif isinstance(obj, list):
            return [self.apply_translations(item, translations) for item in obj]
        elif isinstance(obj, str) and obj in translations:
            return translations[obj]
        else:
            return obj
    
    def translate_json_file(self, input_file: str, output_file: str) -> bool:
        """Translate a single JSON file"""
        try:
            print(f"\nProcessing {input_file}...")
            
            # Read the original JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract all Thai texts
            thai_texts_with_paths = self.extract_thai_texts(data)
            thai_texts = [text for path, text in thai_texts_with_paths]
            
            if not thai_texts:
                print(f"No Thai text found in {input_file}")
                return False
            
            print(f"Found {len(thai_texts)} Thai texts to translate")
            
            # Translate texts in batches for efficiency
            batch_size = 10
            all_translations = {}
            
            for i in range(0, len(thai_texts), batch_size):
                batch = thai_texts[i:i+batch_size]
                print(f"Translating batch {i//batch_size + 1}/{(len(thai_texts)-1)//batch_size + 1}")
                
                translated_batch = self.translate_batch_texts(batch)
                
                for original, translated in zip(batch, translated_batch):
                    all_translations[original] = translated
            
            # Apply translations to the data
            translated_data = self.apply_translations(data, all_translations)
            
            # Write the translated JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(translated_data, f, ensure_ascii=False, indent=2)
            
            print(f"Successfully translated {input_file} -> {output_file}")
            return True
            
        except Exception as e:
            print(f"Error processing {input_file}: {e}")
            return False
    
    def translate_all_json_files(self, directory: str = ".") -> None:
        """Translate all JSON files in the directory"""
        json_files = [f for f in os.listdir(directory) 
                     if f.endswith('.json') and not f.endswith('_translated.json')]
        
        if not json_files:
            print("No JSON files found in the directory.")
            return
        
        print(f"Found {len(json_files)} JSON files to translate:")
        for file in json_files:
            print(f"  - {file}")
        
        print("\nStarting translation process...")
        
        success_count = 0
        for json_file in json_files:
            input_path = os.path.join(directory, json_file)
            output_filename = json_file.replace('.json', '_translated.json')
            output_path = os.path.join(directory, output_filename)
            
            if self.translate_json_file(input_path, output_path):
                success_count += 1
        
        print(f"\nTranslation completed!")
        print(f"Successfully translated {success_count}/{len(json_files)} files.")

def main():
    # You can set your API key here or use environment variable
    api_key = input("Please enter your Google AI API key (or press Enter to use GOOGLE_API_KEY env var): ").strip()
    if not api_key:
        api_key = None
    
    try:
        translator = JSONThaiTranslatorGemini(api_key=api_key)
        translator.translate_all_json_files()
    except ValueError as e:
        print(f"Error: {e}")
        print("Please get your API key from: https://makersuite.google.com/app/apikey")

if __name__ == "__main__":
    main()
