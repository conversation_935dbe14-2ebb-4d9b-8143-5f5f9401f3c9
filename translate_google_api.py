#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fast Google Translate API SMS Translator
Much faster than LLM-based translation
"""

import json
import os
import re
from typing import Dict, Any, List
from googletrans import Translator
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class FastGoogleTranslator:
    def __init__(self, max_workers: int = 10):
        """Initialize with Google Translate API"""
        self.translator = Translator()
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        self.max_workers = max_workers
        
        # Thread-safe progress tracking
        self.progress_lock = threading.Lock()
        self.translated_count = 0
        self.total_count = 0
        
        print(f"🚀 Fast Google Translate API")
        print(f"   🔄 Workers: {self.max_workers}")
        print(f"   ⚡ Expected speed: 100-500 SMS/second")
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_single_message(self, message_data: tuple) -> tuple:
        """Translate a single SMS message"""
        index, content = message_data
        
        if not self.is_thai_text(content):
            return index, content, content  # Return original if not Thai
        
        try:
            # Add small random delay to avoid rate limiting
            time.sleep(0.01 + (index % 10) * 0.001)
            
            result = self.translator.translate(content, src='th', dest='en')
            translated = result.text
            
            # Update progress
            with self.progress_lock:
                self.translated_count += 1
                if self.translated_count % 50 == 0:  # Update every 50 translations
                    progress = (self.translated_count / self.total_count * 100) if self.total_count > 0 else 0
                    print(f"🔄 Progress: {self.translated_count}/{self.total_count} ({progress:.1f}%)")
            
            return index, content, translated
            
        except Exception as e:
            print(f"❌ Translation error for message {index}: {e}")
            return index, content, content  # Return original on error
    
    def translate_batch_parallel(self, sms_contents: List[str]) -> Dict[str, str]:
        """Translate multiple messages in parallel"""
        thai_messages = [(i, content) for i, content in enumerate(sms_contents) 
                        if self.is_thai_text(content)]
        
        if not thai_messages:
            return {}
        
        print(f"🔄 Translating {len(thai_messages)} Thai messages with {self.max_workers} workers...")
        
        translations = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all translation tasks
            future_to_message = {
                executor.submit(self.translate_single_message, msg_data): msg_data[0] 
                for msg_data in thai_messages
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_message):
                try:
                    index, original, translated = future.result()
                    translations[original] = translated
                except Exception as e:
                    print(f"❌ Future error: {e}")
        
        return translations
    
    def translate_file_fast(self, input_file: str) -> bool:
        """Translate file using fast Google Translate API"""
        try:
            print(f"\n📱 Fast processing: {input_file}")
            start_time = time.time()
            
            # Read JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract SMS records
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print(f"❌ No SMS records found")
                return False
            
            # Collect all SMS contents
            all_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content:
                    all_contents.append(content)
            
            thai_contents = [content for content in all_contents if self.is_thai_text(content)]
            
            if not thai_contents:
                print(f"❌ No Thai SMS content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            print(f"📊 Total SMS records: {len(sms_records)}")
            
            self.total_count = len(thai_contents)
            self.translated_count = 0
            
            # Translate all messages in parallel
            all_translations = self.translate_batch_parallel(all_contents)
            
            # Apply translations to the data
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations and content != all_translations[content]:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save result
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_google_translated.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Performance stats
            total_time = time.time() - start_time
            messages_per_second = len(thai_contents) / total_time if total_time > 0 else 0
            success_rate = translated_count / len(thai_contents) * 100 if thai_contents else 0
            
            print(f"\n🎉 Fast translation completed!")
            print(f"✅ Translated: {translated_count}/{len(thai_contents)} messages")
            print(f"📈 Success rate: {success_rate:.1f}%")
            print(f"⏱️  Total time: {total_time:.1f} seconds")
            print(f"🚀 Speed: {messages_per_second:.1f} messages/second")
            print(f"📄 Output: {output_filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def install_googletrans():
    """Install googletrans if not available"""
    try:
        import googletrans
        return True
    except ImportError:
        print("📦 Installing googletrans...")
        import subprocess
        try:
            subprocess.check_call(['pip', 'install', 'googletrans==4.0.0rc1'])
            print("✅ googletrans installed successfully")
            return True
        except:
            print("❌ Failed to install googletrans")
            print("💡 Please run: pip install googletrans==4.0.0rc1")
            return False

def main():
    print("🚀 Fast Google Translate API SMS Translator")
    print("=" * 60)
    print("⚡ Much faster than LLM-based translation!")
    print()
    
    # Check if googletrans is installed
    if not install_googletrans():
        return
    
    # Configuration options
    print("Choose speed configuration:")
    print("1. 🐌 Conservative (5 workers)")
    print("2. 🚀 Fast (10 workers)")
    print("3. ⚡ Turbo (20 workers)")
    print("4. 🔥 Maximum (50 workers)")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    worker_configs = {
        "1": 5,
        "2": 10,
        "3": 20,
        "4": 50
    }
    
    max_workers = worker_configs.get(choice, 10)
    
    # List files
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print(f"\n📋 Available files:")
    for i, file in enumerate(json_files, 1):
        size_kb = os.path.getsize(file) / 1024
        # Estimate translation time with Google Translate
        est_messages = size_kb * 2.5
        est_time_seconds = est_messages / (max_workers * 5)  # Assume 5 msg/sec per worker
        
        if est_time_seconds < 60:
            time_str = f"{est_time_seconds:.0f}s"
        else:
            time_str = f"{est_time_seconds/60:.1f}m"
        
        print(f"   {i}. {file} ({size_kb:.0f}KB) - Est: {time_str}")
    
    try:
        file_choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[file_choice]
        
        print(f"\n🚀 Starting fast translation with {max_workers} workers...")
        
        translator = FastGoogleTranslator(max_workers=max_workers)
        success = translator.translate_file_fast(selected_file)
        
        if success:
            print(f"\n🎉 Fast translation completed!")
            print(f"💡 Google Translate API is much faster than LLM!")
        else:
            print(f"\n❌ Translation failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
