#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rate Limit Optimizer for Google Gemini API
Optimizes settings based on RPM=4000, TPM=4,000,000 limits
"""

import os
import json
import math
from dotenv import load_dotenv

class RateLimitOptimizer:
    def __init__(self):
        self.rpm_limit = 4000  # Requests per minute
        self.tpm_limit = 4000000  # Tokens per minute
        
        # Estimated token usage per SMS message
        self.avg_thai_sms_tokens = 50  # Thai SMS average
        self.avg_english_tokens = 40   # English response average
        self.prompt_overhead_tokens = 100  # System prompt tokens
        
    def calculate_optimal_settings(self):
        """Calculate optimal batch size and timing"""
        print("🔧 Rate Limit Optimizer")
        print("=" * 50)
        print(f"📊 API Limits:")
        print(f"   RPM: {self.rpm_limit:,} requests/minute")
        print(f"   TPM: {self.tpm_limit:,} tokens/minute")
        print()
        
        # Calculate token usage per batch for different batch sizes
        batch_options = [5, 10, 15, 20, 25, 30, 40, 50]
        
        print("📈 Batch Size Analysis:")
        print("Batch | Tokens/Batch | Max Batches/Min | SMS/Min | Efficiency")
        print("-" * 65)
        
        best_config = None
        max_sms_per_min = 0
        
        for batch_size in batch_options:
            # Calculate tokens per batch
            input_tokens = (batch_size * self.avg_thai_sms_tokens) + self.prompt_overhead_tokens
            output_tokens = batch_size * self.avg_english_tokens
            total_tokens_per_batch = input_tokens + output_tokens
            
            # Calculate limits
            max_batches_by_rpm = self.rpm_limit
            max_batches_by_tpm = self.tpm_limit // total_tokens_per_batch
            
            # Actual limit is the smaller one
            max_batches_per_min = min(max_batches_by_rpm, max_batches_by_tpm)
            sms_per_min = max_batches_per_min * batch_size
            
            # Efficiency: how close we get to using full capacity
            rpm_usage = max_batches_per_min / self.rpm_limit * 100
            tpm_usage = (max_batches_per_min * total_tokens_per_batch) / self.tpm_limit * 100
            efficiency = min(rpm_usage, tpm_usage)
            
            print(f"{batch_size:5d} | {total_tokens_per_batch:11d} | {max_batches_per_min:13d} | {sms_per_min:7d} | {efficiency:8.1f}%")
            
            if sms_per_min > max_sms_per_min:
                max_sms_per_min = sms_per_min
                best_config = {
                    'batch_size': batch_size,
                    'max_batches_per_min': max_batches_per_min,
                    'sms_per_min': sms_per_min,
                    'tokens_per_batch': total_tokens_per_batch,
                    'efficiency': efficiency
                }
        
        print()
        print("🎯 Optimal Configuration:")
        print(f"   Batch Size: {best_config['batch_size']}")
        print(f"   Max SMS/minute: {best_config['sms_per_min']:,}")
        print(f"   Efficiency: {best_config['efficiency']:.1f}%")
        
        # Calculate optimal delay
        optimal_delay = 60 / best_config['max_batches_per_min']
        print(f"   Optimal delay: {optimal_delay:.2f} seconds")
        
        return best_config
    
    def create_optimized_env(self, config):
        """Create optimized .env file"""
        # Calculate safe delay (add 10% buffer)
        safe_delay = (60 / config['max_batches_per_min']) * 1.1
        
        # Read current settings
        load_dotenv()
        api_key = os.getenv('GOOGLE_API_KEY', 'your_google_api_key_here')
        model_name = os.getenv('MODEL_NAME', 'gemini-2.5-flash-preview-05-20')
        
        env_content = f"""# Rate Limit Optimized Configuration
# RPM: 4000, TPM: 4,000,000
GOOGLE_API_KEY={api_key}
GOOGLE_API_URL=https://generativelanguage.googleapis.com
MODEL_NAME={model_name}

# Optimized for rate limits
BATCH_SIZE={config['batch_size']}
RATE_LIMIT_DELAY={safe_delay:.2f}

# Performance stats (estimated)
# Max SMS per minute: {config['sms_per_min']}
# Efficiency: {config['efficiency']:.1f}%
"""
        
        with open('.env.optimized', 'w') as f:
            f.write(env_content)
        
        print(f"\n✅ Created .env.optimized")
        print(f"📦 Batch size: {config['batch_size']}")
        print(f"⏱️  Delay: {safe_delay:.2f}s (with 10% safety buffer)")
        print(f"🚀 Expected speed: {config['sms_per_min']:,} SMS/minute")
        
        return safe_delay
    
    def estimate_file_times(self, sms_per_minute):
        """Estimate translation times for files"""
        json_files = [f for f in os.listdir('.') 
                     if f.endswith('.json') and not f.endswith('_translated.json')]
        
        if not json_files:
            return
        
        print(f"\n⏱️  Estimated Translation Times:")
        print("File | Size | Est. SMS | Time")
        print("-" * 40)
        
        for file in json_files:
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
                actual_sms = len([r for r in sms_records if r.get('content', '')])
                
                minutes = actual_sms / sms_per_minute
                
                if minutes < 60:
                    time_str = f"{minutes:.1f}m"
                else:
                    hours = minutes / 60
                    time_str = f"{hours:.1f}h"
                
                size_kb = os.path.getsize(file) / 1024
                print(f"{file[:20]:20} | {size_kb:4.0f}KB | {actual_sms:8d} | {time_str:>6}")
                
            except:
                size_kb = os.path.getsize(file) / 1024
                est_sms = int(size_kb * 2.5)  # Rough estimate
                minutes = est_sms / sms_per_minute
                time_str = f"{minutes:.1f}m" if minutes < 60 else f"{minutes/60:.1f}h"
                print(f"{file[:20]:20} | {size_kb:4.0f}KB | {est_sms:8d} | {time_str:>6}")

def create_parallel_rate_limited():
    """Create rate-limited parallel translator"""
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rate-Limited Parallel Translator
Respects RPM=4000, TPM=4,000,000 limits
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

load_dotenv()

class RateLimitedTranslator:
    def __init__(self):
        # Load optimized settings
        self.batch_size = int(os.getenv('BATCH_SIZE', '25'))
        self.rate_limit_delay = float(os.getenv('RATE_LIMIT_DELAY', '0.9'))
        
        # Rate limiting
        self.request_times = queue.Queue()
        self.lock = threading.Lock()
        
        # Configure API
        api_key = os.getenv('GOOGLE_API_KEY')
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.5-flash-preview-05-20')
        self.thai_pattern = re.compile(r'[\\u0E00-\\u0E7F]+')
        
        print(f"🚀 Rate-Limited Translator")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Delay: {self.rate_limit_delay}s")
    
    def wait_for_rate_limit(self):
        """Ensure we don't exceed rate limits"""
        with self.lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            while not self.request_times.empty():
                if now - self.request_times.queue[0] > 60:
                    self.request_times.get()
                else:
                    break
            
            # Check if we can make a request
            if self.request_times.qsize() >= 4000:  # RPM limit
                sleep_time = 60 - (now - self.request_times.queue[0])
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            # Add current request time
            self.request_times.put(now)
    
    def translate_batch(self, batch_contents):
        """Translate a batch with rate limiting"""
        self.wait_for_rate_limit()
        
        model = genai.GenerativeModel(self.model_name)
        thai_contents = [c for c in batch_contents if self.thai_pattern.search(c or '')]
        
        if not thai_contents:
            return {}
        
        try:
            time.sleep(self.rate_limit_delay)
            
            numbered_texts = "\\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_contents)])
            prompt = f"""Translate these Thai SMS to English, same numbered format:
            
{numbered_texts}"""
            
            response = model.generate_content(prompt)
            
            # Parse response
            translations = {}
            for line in response.text.strip().split('\\n'):
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_contents):
                            translations[thai_contents[num]] = translation.strip()
                    except:
                        continue
            
            return translations
            
        except Exception as e:
            print(f"❌ Translation error: {e}")
            return {}
    
    def translate_file(self, filename):
        """Translate file with rate limiting"""
        print(f"\\n📱 Processing {filename}...")
        
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
        thai_contents = [r.get('content', '') for r in sms_records 
                        if r.get('content', '') and self.thai_pattern.search(r.get('content', ''))]
        
        print(f"📊 Found {len(thai_contents)} Thai SMS messages")
        
        # Process in batches
        all_translations = {}
        total_batches = (len(thai_contents) - 1) // self.batch_size + 1
        
        for i in range(0, len(thai_contents), self.batch_size):
            batch = thai_contents[i:i+self.batch_size]
            batch_num = i // self.batch_size + 1
            
            print(f"🔄 Batch {batch_num}/{total_batches}")
            translations = self.translate_batch(batch)
            all_translations.update(translations)
        
        # Apply translations
        for record in sms_records:
            content = record.get('content', '')
            if content in all_translations:
                record['content'] = all_translations[content]
        
        # Save result
        output_file = f"output/{filename.replace('.json', '_rate_limited.json')}"
        os.makedirs('output', exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Saved: {output_file}")
        print(f"📊 Translated: {len(all_translations)} messages")

def main():
    translator = RateLimitedTranslator()
    
    files = [f for f in os.listdir('.') if f.endswith('.json') and not f.endswith('_translated.json')]
    
    for i, file in enumerate(files, 1):
        print(f"{i}. {file}")
    
    choice = int(input("Choose file: ")) - 1
    translator.translate_file(files[choice])

if __name__ == "__main__":
    main()
'''
    
    with open('translate_rate_limited.py', 'w') as f:
        f.write(script_content)
    
    print("✅ Created translate_rate_limited.py")

def main():
    optimizer = RateLimitOptimizer()
    
    print("Choose option:")
    print("1. 📊 Analyze optimal settings")
    print("2. ⚙️  Create optimized config")
    print("3. 🚀 Create rate-limited translator")
    print("4. 📈 All of the above")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice in ["1", "4"]:
        config = optimizer.calculate_optimal_settings()
        
        if choice in ["2", "4"]:
            optimizer.create_optimized_env(config)
            optimizer.estimate_file_times(config['sms_per_min'])
        
        if choice in ["3", "4"]:
            create_parallel_rate_limited()
            
    elif choice == "2":
        config = optimizer.calculate_optimal_settings()
        optimizer.create_optimized_env(config)
        optimizer.estimate_file_times(config['sms_per_min'])
        
    elif choice == "3":
        create_parallel_rate_limited()

if __name__ == "__main__":
    main()
