#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parallel SMS JSON Thai to English Translator
Uses multiple threads for faster translation
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# Load environment variables from .env file
load_dotenv()

class ParallelSMSTranslator:
    def __init__(self, max_workers: int = 3):
        """Initialize with multiple workers"""
        # Load configuration
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key or api_key == 'your_google_api_key_here':
            raise ValueError("Please set your Google API key in the .env file")
        
        self.model_name = os.getenv('MODEL_NAME', 'gemini-pro')
        self.batch_size = int(os.getenv('BATCH_SIZE', '10'))
        self.rate_limit_delay = float(os.getenv('RATE_LIMIT_DELAY', '0.3'))
        self.max_workers = max_workers
        
        # Configure Google AI
        genai.configure(api_key=api_key)
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # Thread-safe progress tracking
        self.progress_lock = threading.Lock()
        self.translated_count = 0
        self.total_count = 0
        
        print(f"🚀 Parallel translator initialized:")
        print(f"   🤖 Model: {self.model_name}")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Rate limit: {self.rate_limit_delay}s")
        print(f"   🔄 Workers: {self.max_workers}")
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_batch_worker(self, batch_data: tuple) -> Dict[str, str]:
        """Worker function for translating a batch"""
        batch_id, batch_contents = batch_data
        
        # Create model instance for this thread
        model = genai.GenerativeModel(self.model_name)
        
        thai_contents = [content for content in batch_contents if self.is_thai_text(content)]
        
        if not thai_contents:
            return {}
        
        try:
            # Stagger requests to avoid hitting rate limits
            time.sleep(batch_id * 0.1 + self.rate_limit_delay)
            
            # Create batch translation prompt
            numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_contents)])
            
            prompt = f"""Please translate the following Thai SMS messages to English. 
            These are SMS text messages, maintain casual tone and special characters.
            Return only the English translations in the same numbered format:
            
            {numbered_texts}"""
            
            response = model.generate_content(prompt)
            translated_lines = response.text.strip().split('\n')
            
            # Parse responses
            translations = {}
            for line in translated_lines:
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_contents):
                            original = thai_contents[num]
                            translations[original] = translation.strip()
                    except:
                        continue
            
            # Update progress thread-safely
            with self.progress_lock:
                self.translated_count += len(translations)
                progress = (self.translated_count / self.total_count * 100) if self.total_count > 0 else 0
                print(f"🔄 Batch {batch_id+1} completed: {len(translations)} translations | Progress: {progress:.1f}%")
            
            return translations
            
        except Exception as e:
            print(f"❌ Batch {batch_id+1} error: {e}")
            return {}
    
    def translate_file_parallel(self, input_file: str) -> bool:
        """Translate file using parallel processing"""
        try:
            print(f"\n📱 Processing {input_file} with parallel translation...")
            
            # Read JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract SMS records
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print(f"❌ No SMS records found")
                return False
            
            # Collect Thai contents
            thai_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content and self.is_thai_text(content):
                    thai_contents.append(content)
            
            if not thai_contents:
                print(f"❌ No Thai SMS content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            self.total_count = len(thai_contents)
            self.translated_count = 0
            
            # Split into batches
            batches = []
            for i in range(0, len(thai_contents), self.batch_size):
                batch = thai_contents[i:i+self.batch_size]
                batches.append((i // self.batch_size, batch))
            
            print(f"🔄 Processing {len(batches)} batches with {self.max_workers} workers...")
            
            # Process batches in parallel
            all_translations = {}
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all batches
                future_to_batch = {
                    executor.submit(self.translate_batch_worker, batch_data): batch_data[0] 
                    for batch_data in batches
                }
                
                # Collect results
                for future in as_completed(future_to_batch):
                    batch_translations = future.result()
                    all_translations.update(batch_translations)
            
            end_time = time.time()
            translation_time = end_time - start_time
            
            # Apply translations
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save translated file
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_parallel_translated.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Performance stats
            messages_per_second = len(all_translations) / translation_time if translation_time > 0 else 0
            
            print(f"\n🎉 Parallel translation completed!")
            print(f"✅ Translated: {len(all_translations)}/{len(thai_contents)} messages")
            print(f"⏱️  Time: {translation_time:.1f} seconds")
            print(f"🚀 Speed: {messages_per_second:.1f} messages/second")
            print(f"📄 Output: {output_filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    print("🚀 Parallel SMS JSON Translator")
    print("=" * 50)
    
    # Configuration options
    print("Choose speed configuration:")
    print("1. 🐌 Conservative (3 workers, batch=10)")
    print("2. 🚀 Fast (5 workers, batch=15)")
    print("3. ⚡ Turbo (8 workers, batch=20)")
    print("4. 🔧 Custom")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "2":
        max_workers, batch_size = 5, 15
    elif choice == "3":
        max_workers, batch_size = 8, 20
    elif choice == "4":
        max_workers = int(input("Workers (3-10): "))
        batch_size = int(input("Batch size (10-30): "))
    else:
        max_workers, batch_size = 3, 10
    
    # Update environment for this session
    os.environ['BATCH_SIZE'] = str(batch_size)
    
    # Select file
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print(f"\n📋 Available files:")
    for i, file in enumerate(json_files, 1):
        size = os.path.getsize(file) / 1024
        print(f"   {i}. {file} ({size:.0f}KB)")
    
    try:
        file_choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[file_choice]
        
        translator = ParallelSMSTranslator(max_workers=max_workers)
        translator.translate_file_parallel(selected_file)
        
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
