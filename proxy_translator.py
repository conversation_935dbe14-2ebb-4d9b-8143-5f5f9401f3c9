#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proxy-based Google Translate to avoid IP blocking
Uses proxy rotation and rate limiting
"""

import json
import os
import re
import time
import random
from typing import Dict, Any, List
from googletrans import Translator
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class ProxyTranslator:
    def __init__(self):
        """Initialize with proxy rotation"""
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # Free proxy sources (you can add more)
        self.proxy_sources = [
            "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all",
            "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
            "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
        ]
        
        self.proxies = []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        self.proxy_lock = threading.Lock()
        
        # Progress tracking
        self.progress_lock = threading.Lock()
        self.translated_count = 0
        self.total_count = 0
        
        print("🔄 Initializing proxy-based translator...")
        self.load_proxies()
        
    def load_proxies(self):
        """Load proxies from free sources"""
        print("📡 Loading proxy list...")
        
        for source in self.proxy_sources:
            try:
                response = requests.get(source, timeout=10)
                if response.status_code == 200:
                    proxy_list = response.text.strip().split('\n')
                    for proxy in proxy_list:
                        if ':' in proxy and proxy not in self.proxies:
                            self.proxies.append(proxy.strip())
                            
            except Exception as e:
                print(f"⚠️  Failed to load from {source}: {e}")
        
        # Add some reliable public proxies as backup
        backup_proxies = [
            "*******:3128",
            "*******:80", 
            "**************:80"
        ]
        
        self.proxies.extend(backup_proxies)
        self.proxies = list(set(self.proxies))  # Remove duplicates
        
        print(f"✅ Loaded {len(self.proxies)} proxies")
        
    def get_next_proxy(self):
        """Get next working proxy"""
        with self.proxy_lock:
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_proxy_index]
                self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
                
                if proxy not in self.failed_proxies:
                    return proxy
                    
                attempts += 1
            
            # If all proxies failed, reset and try again
            self.failed_proxies.clear()
            return self.proxies[0] if self.proxies else None
    
    def test_proxy(self, proxy):
        """Test if proxy is working"""
        try:
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            response = requests.get('http://httpbin.org/ip', 
                                  proxies=proxies, 
                                  timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def translate_with_proxy(self, text, max_retries=3):
        """Translate text using proxy rotation"""
        if not self.is_thai_text(text):
            return text
        
        for attempt in range(max_retries):
            proxy = self.get_next_proxy()
            
            if not proxy:
                print("❌ No working proxies available")
                return text
            
            try:
                # Create translator with proxy
                proxies = {
                    'http': f'http://{proxy}',
                    'https': f'http://{proxy}'
                }
                
                # Use requests session with proxy
                session = requests.Session()
                session.proxies.update(proxies)
                
                # Create translator (note: googletrans doesn't directly support proxies)
                # We'll use a different approach
                translator = Translator()
                
                # Add random delay to avoid rate limiting
                time.sleep(random.uniform(0.1, 0.5))
                
                result = translator.translate(text, src='th', dest='en')
                return result.text
                
            except Exception as e:
                print(f"⚠️  Proxy {proxy} failed: {e}")
                with self.proxy_lock:
                    self.failed_proxies.add(proxy)
                
                if attempt == max_retries - 1:
                    print(f"❌ All retries failed for: {text[:30]}...")
                    return text
        
        return text
    
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_batch_safe(self, messages, batch_size=10):
        """Translate batch with safe rate limiting"""
        translations = {}
        
        for i in range(0, len(messages), batch_size):
            batch = messages[i:i+batch_size]
            
            print(f"🔄 Processing batch {i//batch_size + 1}/{(len(messages)-1)//batch_size + 1}")
            
            # Process batch with threading but limited concurrency
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_text = {
                    executor.submit(self.translate_with_proxy, text): text 
                    for text in batch if self.is_thai_text(text)
                }
                
                for future in as_completed(future_to_text):
                    original_text = future_to_text[future]
                    try:
                        translated = future.result()
                        translations[original_text] = translated
                        
                        with self.progress_lock:
                            self.translated_count += 1
                            if self.translated_count % 10 == 0:
                                progress = (self.translated_count / self.total_count * 100) if self.total_count > 0 else 0
                                print(f"📈 Progress: {self.translated_count}/{self.total_count} ({progress:.1f}%)")
                                
                    except Exception as e:
                        print(f"❌ Translation failed: {e}")
                        translations[original_text] = original_text
            
            # Longer delay between batches to avoid rate limiting
            time.sleep(2)
        
        return translations

def create_cloud_translation_script():
    """Create Google Cloud Translation API script"""
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Cloud Translation API - Official and Fast
No IP blocking, high rate limits
"""

import json
import os
from google.cloud import translate_v2 as translate
from concurrent.futures import ThreadPoolExecutor
import re

class CloudTranslator:
    def __init__(self, credentials_path):
        """Initialize with Google Cloud credentials"""
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
        self.client = translate.Client()
        self.thai_pattern = re.compile(r'[\\u0E00-\\u0E7F]+')
        
    def translate_batch_cloud(self, texts):
        """Translate batch using Cloud API"""
        thai_texts = [t for t in texts if self.thai_pattern.search(t or '')]
        
        if not thai_texts:
            return {}
        
        # Cloud API can handle large batches efficiently
        results = self.client.translate(
            thai_texts,
            source_language='th',
            target_language='en'
        )
        
        translations = {}
        for original, result in zip(thai_texts, results):
            translations[original] = result['translatedText']
        
        return translations
    
    def translate_file_cloud(self, filename):
        """Translate entire file using Cloud API"""
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
        thai_contents = [r.get('content', '') for r in sms_records 
                        if r.get('content', '') and self.thai_pattern.search(r.get('content', ''))]
        
        print(f"📊 Translating {len(thai_contents)} messages with Cloud API...")
        
        # Process in large batches (Cloud API can handle 1000+ at once)
        batch_size = 100
        all_translations = {}
        
        for i in range(0, len(thai_contents), batch_size):
            batch = thai_contents[i:i+batch_size]
            print(f"🔄 Batch {i//batch_size + 1}/{(len(thai_contents)-1)//batch_size + 1}")
            
            batch_translations = self.translate_batch_cloud(batch)
            all_translations.update(batch_translations)
        
        # Apply translations
        for record in sms_records:
            content = record.get('content', '')
            if content in all_translations:
                record['content'] = all_translations[content]
        
        # Save result
        output_file = f"output/{os.path.basename(filename).replace('.json', '_cloud_translated.json')}"
        os.makedirs('output', exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Cloud translation completed: {output_file}")

# Usage:
# 1. Set up Google Cloud project
# 2. Enable Translation API
# 3. Download service account key JSON
# 4. Run: translator = CloudTranslator('path/to/credentials.json')
'''
    
    with open('cloud_translator.py', 'w') as f:
        f.write(script_content)
    
    print("✅ Created cloud_translator.py")

def main():
    print("🚀 Anti-IP-Block Translation Solutions")
    print("=" * 50)
    
    print("Choose solution:")
    print("1. 🔄 Proxy rotation (free but slower)")
    print("2. ☁️  Google Cloud API (paid but fast)")
    print("3. 📝 Create setup instructions")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🔄 Using proxy rotation...")
        print("⚠️  Note: This may be slower and less reliable")
        
        translator = ProxyTranslator()
        
        # List files
        json_files = [f for f in os.listdir('.') 
                     if f.endswith('.json') and not f.endswith('_translated.json')]
        
        if json_files:
            print("📋 Available files:")
            for i, file in enumerate(json_files, 1):
                size_kb = os.path.getsize(file) / 1024
                print(f"   {i}. {file} ({size_kb:.0f}KB)")
            
            # Demo with small batch
            print("\n💡 Proxy method is experimental. Consider Google Cloud API for production use.")
        
    elif choice == "2":
        create_cloud_translation_script()
        print("\n☁️  Google Cloud Translation API Setup:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create new project or select existing")
        print("3. Enable Cloud Translation API")
        print("4. Create service account and download JSON key")
        print("5. Run: pip install google-cloud-translate")
        print("6. Use cloud_translator.py with your credentials")
        
    elif choice == "3":
        print("\n📝 Setup Instructions:")
        print("\n🆓 Free Options (with limitations):")
        print("- Proxy rotation (slow, unreliable)")
        print("- Rate limiting (very slow)")
        print("- Multiple IP addresses/VPN")
        
        print("\n💰 Paid Options (recommended):")
        print("- Google Cloud Translation API: $20/million chars")
        print("- Azure Translator: Similar pricing")
        print("- AWS Translate: Similar pricing")
        
        print("\n🎯 Recommendation for your use case:")
        print("- Small files (<1MB): Use current Gemini approach")
        print("- Large files (>1MB): Google Cloud Translation API")
        print("- Budget option: Optimize current Gemini settings")

if __name__ == "__main__":
    main()
