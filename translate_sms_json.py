#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMS JSON Thai to English Translator using Google Gemini
Specifically designed for SMS record JSON files
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time

class SMSJSONTranslator:
    def __init__(self, api_key: str = None):
        """Initialize the translator with Google Gemini API"""
        if api_key is None:
            api_key = os.getenv('GOOGLE_API_KEY')
            if not api_key:
                raise ValueError("Please provide Google API key or set GOOGLE_API_KEY environment variable")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')  # Thai Unicode range
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def translate_sms_batch(self, sms_contents: List[str]) -> Dict[str, str]:
        """Translate a batch of SMS contents"""
        thai_contents = [content for content in sms_contents if self.is_thai_text(content)]
        
        if not thai_contents:
            return {}
        
        try:
            time.sleep(1)  # Rate limiting
            
            # Create batch translation prompt
            numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_contents)])
            
            prompt = f"""Please translate the following Thai SMS messages to English. 
            These are SMS text messages, so please maintain the casual tone and any special characters.
            Return only the English translations in the same numbered format:
            
            {numbered_texts}"""
            
            response = self.model.generate_content(prompt)
            translated_lines = response.text.strip().split('\n')
            
            # Parse the numbered responses
            translations = {}
            for line in translated_lines:
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_contents):
                            original = thai_contents[num]
                            translations[original] = translation.strip()
                            print(f"✓ Translated SMS: '{original[:40]}...' -> '{translation[:40]}...'")
                    except:
                        continue
            
            return translations
            
        except Exception as e:
            print(f"Batch translation error: {e}")
            return {}
    
    def translate_json_file(self, input_file: str, output_file: str) -> bool:
        """Translate SMS content in a JSON file"""
        try:
            print(f"\n📱 Processing {input_file}...")
            
            # Read the original JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract SMS contents from mobileSmsRecords
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print(f"No SMS records found in {input_file}")
                return False
            
            # Collect all SMS contents
            all_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content:
                    all_contents.append(content)
            
            thai_contents = [content for content in all_contents if self.is_thai_text(content)]
            
            if not thai_contents:
                print(f"No Thai SMS content found in {input_file}")
                return False
            
            print(f"Found {len(thai_contents)} Thai SMS messages to translate")
            
            # Translate in batches
            batch_size = 5  # Smaller batches for SMS to avoid token limits
            all_translations = {}
            
            for i in range(0, len(thai_contents), batch_size):
                batch = thai_contents[i:i+batch_size]
                print(f"🔄 Translating batch {i//batch_size + 1}/{(len(thai_contents)-1)//batch_size + 1}")
                
                batch_translations = self.translate_sms_batch(batch)
                all_translations.update(batch_translations)
            
            # Apply translations to the data
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Write the translated JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Successfully translated {translated_count} SMS messages")
            print(f"📄 Output saved to: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error processing {input_file}: {e}")
            return False
    
    def translate_all_json_files(self, directory: str = ".") -> None:
        """Translate all JSON files in the directory"""
        json_files = [f for f in os.listdir(directory) 
                     if f.endswith('.json') and not f.endswith('_translated.json')]
        
        if not json_files:
            print("❌ No JSON files found in the directory.")
            return
        
        print(f"📋 Found {len(json_files)} JSON files to translate:")
        for file in json_files:
            print(f"   📄 {file}")
        
        print(f"\n🚀 Starting translation process...")
        
        success_count = 0
        total_files = len(json_files)
        
        for i, json_file in enumerate(json_files, 1):
            print(f"\n📊 Progress: {i}/{total_files}")
            input_path = os.path.join(directory, json_file)
            output_filename = json_file.replace('.json', '_translated.json')
            output_path = os.path.join(directory, output_filename)
            
            if self.translate_json_file(input_path, output_path):
                success_count += 1
        
        print(f"\n🎉 Translation completed!")
        print(f"✅ Successfully translated {success_count}/{total_files} files.")
        
        if success_count > 0:
            print(f"\n📁 Translated files:")
            for json_file in json_files:
                output_filename = json_file.replace('.json', '_translated.json')
                if os.path.exists(output_filename):
                    print(f"   📄 {output_filename}")

def main():
    print("🌐 SMS JSON Thai to English Translator")
    print("=" * 50)
    
    # Get API key
    api_key = input("🔑 Please enter your Google AI API key (or press Enter to use GOOGLE_API_KEY env var): ").strip()
    if not api_key:
        api_key = None
    
    try:
        translator = SMSJSONTranslator(api_key=api_key)
        translator.translate_all_json_files()
    except ValueError as e:
        print(f"❌ Error: {e}")
        print("🔗 Get your API key from: https://makersuite.google.com/app/apikey")
    except KeyboardInterrupt:
        print("\n⏹️  Translation stopped by user.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
