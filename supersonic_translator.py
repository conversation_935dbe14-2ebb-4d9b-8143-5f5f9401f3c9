#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Supersonic Translator - Maximum Speed Optimization
Pushes the limits of your API quotas for ultimate speed
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import threading
import asyncio
import aiohttp

load_dotenv()

class SupersonicTranslator:
    def __init__(self):
        """Initialize supersonic translator with extreme optimizations"""
        # API setup
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("Please set your Google API key")
        
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.0-flash-lite')
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # EXTREME optimization settings
        self.max_workers = 50      # Maximum parallel requests
        self.batch_size = 150      # Huge batches to maximize token efficiency
        self.min_delay = 0.01      # Minimal delay (push rate limits)
        self.chunk_size = 5        # Process multiple batches per worker
        
        # Rate limiting (aggressive)
        self.request_times = []
        self.request_lock = threading.Lock()
        self.max_requests_per_minute = 3950  # Leave small buffer
        
        # Progress tracking
        self.progress_lock = threading.Lock()
        self.translated_count = 0
        self.total_count = 0
        self.start_time = None
        
        print(f"🚀 SUPERSONIC Translator - MAXIMUM SPEED")
        print(f"   🤖 Model: {self.model_name}")
        print(f"   🔄 Workers: {self.max_workers}")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Min delay: {self.min_delay}s")
        print(f"   🎯 Target: {self.max_requests_per_minute} RPM")
        print(f"   ⚡ Expected: 50-100 messages/second")
        
    def is_thai_text(self, text: str) -> bool:
        """Ultra-fast Thai detection"""
        return bool(self.thai_pattern.search(text or ''))
    
    def aggressive_rate_limit(self):
        """Aggressive rate limiting for maximum throughput"""
        with self.request_lock:
            now = time.time()
            
            # Clean old requests (older than 60 seconds)
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Check if we're at the limit
            if len(self.request_times) >= self.max_requests_per_minute:
                # Calculate minimum wait time
                oldest_request = self.request_times[0]
                wait_time = 60 - (now - oldest_request)
                if wait_time > 0:
                    time.sleep(wait_time)
                    # Clean again after waiting
                    now = time.time()
                    self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Add current request
            self.request_times.append(now)
    
    def create_ultra_compact_prompt(self, messages: List[str]) -> str:
        """Ultra-compact prompt to save tokens"""
        # Minimize prompt overhead - every token counts!
        numbered = "\n".join([f"{i+1}.{text}" for i, text in enumerate(messages)])
        return f"TH→EN:\n{numbered}"
    
    def translate_mega_batch_supersonic(self, batch_id: int, messages: List[str]) -> Dict[str, str]:
        """Supersonic batch translation with extreme optimization"""
        thai_messages = [msg for msg in messages if self.is_thai_text(msg)]
        
        if not thai_messages:
            return {}
        
        try:
            # Aggressive rate limiting
            self.aggressive_rate_limit()
            time.sleep(self.min_delay)
            
            # Create model instance
            model = genai.GenerativeModel(self.model_name)
            
            # Ultra-compact prompt
            prompt = self.create_ultra_compact_prompt(thai_messages)
            
            # Estimate tokens
            estimated_tokens = len(prompt) * 0.25
            
            print(f"⚡ Batch {batch_id}: {len(thai_messages)} msgs, ~{estimated_tokens:.0f}t")
            
            # Generate with minimal configuration for speed
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=8000,
                    temperature=0.1,  # Lower temperature for faster generation
                )
            )
            
            # Ultra-fast parsing
            translations = {}
            lines = response.text.strip().split('\n')
            
            for line in lines:
                if '.' in line:
                    try:
                        # Find first dot
                        dot_pos = line.find('.')
                        if dot_pos > 0:
                            num_str = line[:dot_pos]
                            translation = line[dot_pos+1:].strip()
                            
                            num = int(num_str) - 1
                            if 0 <= num < len(thai_messages) and translation:
                                original = thai_messages[num]
                                translations[original] = translation
                    except:
                        continue
            
            # Update progress
            with self.progress_lock:
                self.translated_count += len(translations)
                if self.start_time:
                    elapsed = time.time() - self.start_time
                    rate = self.translated_count / elapsed if elapsed > 0 else 0
                    progress = (self.translated_count / self.total_count * 100) if self.total_count > 0 else 0
                    print(f"🚀 B{batch_id}: {len(translations)}/{len(thai_messages)} | {progress:.1f}% | {rate:.1f}/s")
            
            return translations
            
        except Exception as e:
            print(f"❌ Batch {batch_id} error: {e}")
            return {}
    
    def process_chunk_supersonic(self, chunk_id: int, batch_chunk: List[tuple]) -> Dict[str, str]:
        """Process multiple batches in one worker for efficiency"""
        all_translations = {}
        
        for batch_id, messages in batch_chunk:
            batch_translations = self.translate_mega_batch_supersonic(batch_id, messages)
            all_translations.update(batch_translations)
            
            # Micro-delay between batches in same worker
            time.sleep(0.005)
        
        return all_translations
    
    def translate_file_supersonic(self, input_file: str) -> bool:
        """Supersonic file translation with extreme parallelization"""
        try:
            print(f"\n🚀 SUPERSONIC processing: {input_file}")
            self.start_time = time.time()
            
            # Read file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print("❌ No SMS records found")
                return False
            
            # Collect Thai messages
            thai_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content and self.is_thai_text(content):
                    thai_contents.append(content)
            
            if not thai_contents:
                print("❌ No Thai content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            self.total_count = len(thai_contents)
            self.translated_count = 0
            
            # Create mega batches
            batches = []
            for i in range(0, len(thai_contents), self.batch_size):
                batch = thai_contents[i:i+self.batch_size]
                batches.append((i // self.batch_size, batch))
            
            # Group batches into chunks for workers
            chunks = []
            for i in range(0, len(batches), self.chunk_size):
                chunk = batches[i:i+self.chunk_size]
                chunks.append((i // self.chunk_size, chunk))
            
            print(f"🚀 Processing {len(batches)} mega-batches in {len(chunks)} chunks with {self.max_workers} workers...")
            print(f"⚡ Expected completion: {len(thai_contents) / 75:.1f} seconds")
            
            # SUPERSONIC parallel processing
            all_translations = {}
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all chunks
                future_to_chunk = {
                    executor.submit(self.process_chunk_supersonic, chunk_id, chunk): chunk_id
                    for chunk_id, chunk in chunks
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_chunk):
                    chunk_translations = future.result()
                    all_translations.update(chunk_translations)
            
            # Apply translations
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save result
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_supersonic.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Final stats
            total_time = time.time() - self.start_time
            messages_per_second = len(all_translations) / total_time
            success_rate = len(all_translations) / len(thai_contents) * 100
            requests_used = len(self.request_times)
            
            print(f"\n🚀 SUPERSONIC translation completed!")
            print(f"✅ Success rate: {success_rate:.1f}% ({len(all_translations)}/{len(thai_contents)})")
            print(f"⏱️  Total time: {total_time:.1f} seconds")
            print(f"🚀 Speed: {messages_per_second:.1f} messages/second")
            print(f"📊 API efficiency: {requests_used} requests used")
            print(f"📄 Output: {output_filename}")
            
            # Performance comparison
            if messages_per_second > 50:
                print(f"🎉 SUPERSONIC ACHIEVED! {messages_per_second:.1f} msg/s")
            elif messages_per_second > 30:
                print(f"🚀 ULTRA-FAST! {messages_per_second:.1f} msg/s")
            else:
                print(f"⚡ Fast! {messages_per_second:.1f} msg/s")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    print("🚀 SUPERSONIC Translator - MAXIMUM SPEED MODE")
    print("=" * 60)
    print("⚡ Target: 50-100 messages/second")
    print("🎯 Pushes API limits to the maximum")
    print("⚠️  Use with caution - may hit rate limits")
    print()
    
    # List files with supersonic estimates
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print("📋 Available files (SUPERSONIC estimates):")
    for i, file in enumerate(json_files, 1):
        size_kb = os.path.getsize(file) / 1024
        est_messages = size_kb * 2.5
        # Supersonic estimate: 50-100 messages/second
        est_time_seconds = est_messages / 75  # Conservative estimate
        
        if est_time_seconds < 60:
            time_str = f"{est_time_seconds:.0f}s"
        else:
            time_str = f"{est_time_seconds/60:.1f}m"
        
        print(f"   {i}. {file} ({size_kb:.0f}KB) - Est: {time_str}")
    
    print(f"\n⚠️  WARNING: This mode pushes API limits to maximum!")
    print(f"💡 If you get rate limit errors, use ultra_fast_gemini.py instead")
    
    try:
        choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[choice]
        
        confirm = input(f"🚀 Start SUPERSONIC translation of {selected_file}? (y/n): ")
        if confirm.lower() != 'y':
            print("❌ Cancelled")
            return
        
        print(f"\n🚀 Starting SUPERSONIC translation...")
        print(f"🎯 Target file: {selected_file}")
        
        translator = SupersonicTranslator()
        success = translator.translate_file_supersonic(selected_file)
        
        if success:
            print(f"\n🎉 SUPERSONIC translation completed!")
            print(f"💡 This is the fastest possible speed with current API limits!")
        else:
            print(f"\n❌ Translation failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
