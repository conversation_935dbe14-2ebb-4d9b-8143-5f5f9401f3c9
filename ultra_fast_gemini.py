#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra-Fast Gemini Translator
Maximizes your RPM=4000, TPM=4,000,000 limits
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import threading
import asyncio

load_dotenv()

class UltraFastGeminiTranslator:
    def __init__(self):
        """Initialize ultra-fast translator"""
        # API setup
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("Please set your Google API key")
        
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.0-flash-lite')
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # Ultra-optimized settings for your limits
        self.max_workers = 20  # Parallel requests
        self.batch_size = 80   # Large batches to maximize token usage
        self.min_delay = 0.02  # Minimal delay (4000 RPM = 66.7 RPS max)
        
        # Rate limiting
        self.request_times = []
        self.request_lock = threading.Lock()
        
        # Progress tracking
        self.progress_lock = threading.Lock()
        self.translated_count = 0
        self.total_count = 0
        
        print(f"⚡ Ultra-Fast Gemini Translator")
        print(f"   🤖 Model: {self.model_name}")
        print(f"   🔄 Workers: {self.max_workers}")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Min delay: {self.min_delay}s")
        print(f"   🎯 Target: 4000 RPM, 4M TPM")
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        return bool(self.thai_pattern.search(text or ''))
    
    def wait_for_rate_limit(self):
        """Smart rate limiting to maximize throughput"""
        with self.request_lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Check if we're approaching the limit
            if len(self.request_times) >= 3900:  # Leave buffer
                sleep_time = 60 - (now - self.request_times[0])
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    # Clean up old requests after sleeping
                    now = time.time()
                    self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Add current request
            self.request_times.append(now)
    
    def create_mega_prompt(self, messages: List[str]) -> str:
        """Create optimized prompt for large batches"""
        numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(messages)])
        
        # Ultra-concise prompt to save tokens
        return f"""Translate Thai SMS to English. Same numbers:

{numbered_texts}"""
    
    def translate_mega_batch(self, batch_id: int, messages: List[str]) -> Dict[str, str]:
        """Translate large batch with ultra optimization"""
        thai_messages = [msg for msg in messages if self.is_thai_text(msg)]
        
        if not thai_messages:
            return {}
        
        try:
            # Rate limiting
            self.wait_for_rate_limit()
            time.sleep(self.min_delay)
            
            # Create model instance for this thread
            model = genai.GenerativeModel(self.model_name)
            
            prompt = self.create_mega_prompt(thai_messages)
            
            # Estimate tokens (rough)
            estimated_tokens = len(prompt) * 0.3  # Rough estimate
            
            print(f"🚀 Batch {batch_id}: {len(thai_messages)} msgs, ~{estimated_tokens:.0f} tokens")
            
            response = model.generate_content(prompt)
            
            # Parse response
            translations = {}
            lines = response.text.strip().split('\n')
            
            for line in lines:
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_messages):
                            original = thai_messages[num]
                            translations[original] = translation.strip()
                    except:
                        continue
            
            # Update progress
            with self.progress_lock:
                self.translated_count += len(translations)
                progress = (self.translated_count / self.total_count * 100) if self.total_count > 0 else 0
                rate = len(translations) / (time.time() - getattr(self, 'start_time', time.time()))
                print(f"✅ Batch {batch_id}: {len(translations)}/{len(thai_messages)} | Progress: {progress:.1f}% | Rate: {rate:.1f}/s")
            
            return translations
            
        except Exception as e:
            print(f"❌ Batch {batch_id} error: {e}")
            return {}
    
    def translate_file_ultra_fast(self, input_file: str) -> bool:
        """Ultra-fast file translation"""
        try:
            print(f"\n⚡ Ultra-fast processing: {input_file}")
            self.start_time = time.time()
            
            # Read file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print("❌ No SMS records found")
                return False
            
            # Collect Thai messages
            thai_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content and self.is_thai_text(content):
                    thai_contents.append(content)
            
            if not thai_contents:
                print("❌ No Thai content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            self.total_count = len(thai_contents)
            self.translated_count = 0
            
            # Create mega batches
            batches = []
            for i in range(0, len(thai_contents), self.batch_size):
                batch = thai_contents[i:i+self.batch_size]
                batches.append((i // self.batch_size, batch))
            
            print(f"🔄 Processing {len(batches)} mega-batches with {self.max_workers} workers...")
            
            # Process all batches in parallel
            all_translations = {}
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all batches
                future_to_batch = {
                    executor.submit(self.translate_mega_batch, batch_id, batch): batch_id
                    for batch_id, batch in batches
                }
                
                # Collect results
                for future in as_completed(future_to_batch):
                    batch_translations = future.result()
                    all_translations.update(batch_translations)
            
            # Apply translations
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save result
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_ultra_fast.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Final stats
            total_time = time.time() - self.start_time
            messages_per_second = len(all_translations) / total_time
            success_rate = len(all_translations) / len(thai_contents) * 100
            
            print(f"\n⚡ Ultra-fast translation completed!")
            print(f"✅ Success rate: {success_rate:.1f}% ({len(all_translations)}/{len(thai_contents)})")
            print(f"⏱️  Total time: {total_time:.1f} seconds")
            print(f"🚀 Speed: {messages_per_second:.1f} messages/second")
            print(f"📊 API efficiency: {len(self.request_times)} requests used")
            print(f"📄 Output: {output_filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    print("⚡ Ultra-Fast Gemini Translator")
    print("=" * 50)
    print("🎯 Maximizes your 4000 RPM, 4M TPM limits")
    print("🚀 Expected speed: 10-50x faster than current")
    print()
    
    # List files with time estimates
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print("📋 Available files (with ultra-fast estimates):")
    for i, file in enumerate(json_files, 1):
        size_kb = os.path.getsize(file) / 1024
        est_messages = size_kb * 2.5
        # Ultra-fast estimate: 20-50 messages/second
        est_time_seconds = est_messages / 30
        
        if est_time_seconds < 60:
            time_str = f"{est_time_seconds:.0f}s"
        else:
            time_str = f"{est_time_seconds/60:.1f}m"
        
        print(f"   {i}. {file} ({size_kb:.0f}KB) - Est: {time_str}")
    
    try:
        choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[choice]
        
        print(f"\n⚡ Starting ultra-fast translation...")
        print(f"🎯 Target file: {selected_file}")
        
        translator = UltraFastGeminiTranslator()
        success = translator.translate_file_ultra_fast(selected_file)
        
        if success:
            print(f"\n🎉 Ultra-fast translation completed!")
            print(f"💡 This should be 10-50x faster than previous methods!")
        else:
            print(f"\n❌ Translation failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
