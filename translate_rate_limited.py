#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rate-Limited Parallel Translator
Respects RPM=4000, TPM=4,000,000 limits
"""

import json
import os
import re
from typing import Dict, Any, List
import google.generativeai as genai
import time
from dotenv import load_dotenv
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

load_dotenv()

class RateLimitedTranslator:
    def __init__(self):
        # Load optimized settings
        self.batch_size = int(os.getenv('BATCH_SIZE', '25'))
        self.rate_limit_delay = float(os.getenv('RATE_LIMIT_DELAY', '0.9'))
        
        # Rate limiting
        self.request_times = queue.Queue()
        self.lock = threading.Lock()
        
        # Configure API
        api_key = os.getenv('GOOGLE_API_KEY')
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.5-flash-preview-05-20')
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        print(f"🚀 Rate-Limited Translator")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Delay: {self.rate_limit_delay}s")
    
    def wait_for_rate_limit(self):
        """Ensure we don't exceed rate limits"""
        with self.lock:
            now = time.time()
            
            # Remove requests older than 1 minute
            while not self.request_times.empty():
                if now - self.request_times.queue[0] > 60:
                    self.request_times.get()
                else:
                    break
            
            # Check if we can make a request
            if self.request_times.qsize() >= 4000:  # RPM limit
                sleep_time = 60 - (now - self.request_times.queue[0])
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            # Add current request time
            self.request_times.put(now)
    
    def translate_batch(self, batch_contents):
        """Translate a batch with rate limiting"""
        self.wait_for_rate_limit()
        
        model = genai.GenerativeModel(self.model_name)
        thai_contents = [c for c in batch_contents if self.thai_pattern.search(c or '')]
        
        if not thai_contents:
            return {}
        
        try:
            time.sleep(self.rate_limit_delay)
            
            numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(thai_contents)])
            prompt = f"""Translate these Thai SMS to English, same numbered format:
            
{numbered_texts}"""
            
            response = model.generate_content(prompt)
            
            # Parse response
            translations = {}
            for line in response.text.strip().split('\n'):
                if '. ' in line:
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(thai_contents):
                            translations[thai_contents[num]] = translation.strip()
                    except:
                        continue
            
            return translations
            
        except Exception as e:
            print(f"❌ Translation error: {e}")
            return {}
    
    def translate_file(self, filename):
        """Translate file with rate limiting"""
        print(f"\n📱 Processing {filename}...")
        
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
        thai_contents = [r.get('content', '') for r in sms_records 
                        if r.get('content', '') and self.thai_pattern.search(r.get('content', ''))]
        
        print(f"📊 Found {len(thai_contents)} Thai SMS messages")
        
        # Process in batches
        all_translations = {}
        total_batches = (len(thai_contents) - 1) // self.batch_size + 1
        
        for i in range(0, len(thai_contents), self.batch_size):
            batch = thai_contents[i:i+self.batch_size]
            batch_num = i // self.batch_size + 1
            
            print(f"🔄 Batch {batch_num}/{total_batches}")
            translations = self.translate_batch(batch)
            all_translations.update(translations)
        
        # Apply translations
        for record in sms_records:
            content = record.get('content', '')
            if content in all_translations:
                record['content'] = all_translations[content]
        
        # Save result
        output_file = f"output/{filename.replace('.json', '_rate_limited.json')}"
        os.makedirs('output', exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Saved: {output_file}")
        print(f"📊 Translated: {len(all_translations)} messages")

def main():
    translator = RateLimitedTranslator()
    
    files = [f for f in os.listdir('.') if f.endswith('.json') and not f.endswith('_translated.json')]
    
    for i, file in enumerate(files, 1):
        print(f"{i}. {file}")
    
    choice = int(input("Choose file: ")) - 1
    translator.translate_file(files[choice])

if __name__ == "__main__":
    main()
