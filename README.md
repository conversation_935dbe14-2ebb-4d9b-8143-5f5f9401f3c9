# JSON 泰语翻译工具

这个工具使用 Google Gemini 大语言模型将 JSON 文件中的泰语文本翻译成英文，同时保持 JSON 文件的原始结构和格式。

## 功能特点

- 🌐 使用 Google Gemini AI 进行高质量翻译
- 📱 专门优化处理 SMS 短信内容
- 📄 保持 JSON 文件原始结构不变
- 🔄 批量处理多个文件
- ⚡ 智能批处理提高效率
- 🛡️ 错误处理和重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 获取 Google AI API Key

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的 Google 账户
3. 创建新的 API Key
4. 复制 API Key 备用

## 使用方法

### 方法一：直接运行（推荐）

```bash
python translate_sms_json.py
```

运行后会提示输入 API Key，然后自动处理当前目录下的所有 JSON 文件。

### 方法二：设置环境变量

```bash
export GOOGLE_API_KEY="your_api_key_here"
python translate_sms_json.py
```

### 方法三：使用通用翻译脚本

如果需要翻译更复杂的 JSON 结构：

```bash
python translate_json_gemini.py
```

## 输出文件

- 原文件：`1322344034725224448.json`
- 翻译后：`1322344034725224448_translated.json`

翻译后的文件会保持原始 JSON 的完整结构，只有泰语文本会被翻译成英文。

## 文件结构

```
.
├── translate_sms_json.py      # SMS 专用翻译脚本（推荐）
├── translate_json_gemini.py   # 通用 JSON 翻译脚本
├── requirements.txt           # 依赖包列表
├── README.md                 # 使用说明
├── 1322344034725224448.json  # 原始文件
└── 1322344034725224448_translated.json  # 翻译后文件
```

## 注意事项

1. **API 费用**：Google Gemini API 可能产生费用，请查看官方定价
2. **速率限制**：脚本已内置延迟机制避免触发 API 限制
3. **网络连接**：需要稳定的网络连接访问 Google AI 服务
4. **文件备份**：建议在翻译前备份原始文件

## 翻译示例

**原始泰语文本：**
```
"<ฉับไว>รหัสยืนยันของคุณคือ: 5528 อย่าแชร์รหัส zf8eD7R86to"
```

**翻译后英文：**
```
"<Quick> Your verification code is: 5528 Don't share the code zf8eD7R86to"
```

## 故障排除

### 常见问题

1. **API Key 错误**
   - 确认 API Key 正确
   - 检查 API Key 是否已激活

2. **网络连接问题**
   - 检查网络连接
   - 确认可以访问 Google 服务

3. **文件格式错误**
   - 确认 JSON 文件格式正确
   - 检查文件编码为 UTF-8

### 错误信息

- `ValueError: Please provide Google API key`：需要提供有效的 API Key
- `Translation error`：翻译过程中出现错误，会保留原文
- `No Thai text found`：文件中没有检测到泰语文本

## 技术细节

- **泰语检测**：使用 Unicode 范围 `\u0E00-\u0E7F` 检测泰语字符
- **批处理**：每批处理 5 条 SMS 消息以优化性能
- **错误处理**：翻译失败时保留原文，确保数据完整性
- **编码**：使用 UTF-8 编码确保正确处理泰语和英语字符

## 支持的文件格式

目前支持包含以下结构的 JSON 文件：
```json
{
  "req": {
    "user": {
      "mobileSmsRecords": [
        {
          "content": "泰语短信内容",
          "name": "发送者",
          "phoneNo": "电话号码"
        }
      ]
    }
  }
}
```

## 许可证

本工具仅供学习和研究使用。使用 Google AI API 需要遵守 Google 的服务条款。
