#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hybrid Translation Strategy
Combines speed and quality for optimal results
"""

import json
import os
import re
from typing import Dict, Any, List, Tuple
import google.generativeai as genai
import time
from dotenv import load_dotenv

load_dotenv()

class HybridTranslator:
    def __init__(self):
        """Initialize hybrid translator"""
        # Configure Gemini for high-quality translation
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key or api_key == 'your_google_api_key_here':
            raise ValueError("Please set your Google API key in the .env file")
        
        genai.configure(api_key=api_key)
        self.model_name = os.getenv('MODEL_NAME', 'gemini-2.0-flash-lite')
        self.model = genai.GenerativeModel(self.model_name)
        self.thai_pattern = re.compile(r'[\u0E00-\u0E7F]+')
        
        # Optimized settings for quality + speed balance
        self.batch_size = 30  # Larger batches for efficiency
        self.delay = 0.2      # Minimal delay
        
        print(f"🎯 Hybrid Translator (Quality + Speed)")
        print(f"   🤖 Model: {self.model_name}")
        print(f"   📦 Batch size: {self.batch_size}")
        print(f"   ⏱️  Delay: {self.delay}s")
        
    def is_thai_text(self, text: str) -> bool:
        """Check if text contains Thai characters"""
        if not isinstance(text, str):
            return False
        return bool(self.thai_pattern.search(text))
    
    def categorize_messages(self, sms_contents: List[str]) -> Dict[str, List[str]]:
        """Categorize messages by type for optimized translation"""
        categories = {
            'banking': [],      # Bank transactions
            'verification': [], # OTP/verification codes
            'promotional': [],  # Marketing/ads
            'notification': [], # Service notifications
            'general': []       # Other messages
        }
        
        # Keywords for categorization
        banking_keywords = ['บช', 'ธนาคาร', 'โอน', 'ถอน', 'ฝาก', 'คงเหลือ', 'บาท']
        verification_keywords = ['รหัส', 'ยืนยัน', 'OTP', 'PIN', 'code']
        promotional_keywords = ['โปรโมชั่น', 'ลด', 'ฟรี', 'แจก', 'ชิง', 'รางวัล']
        notification_keywords = ['แจ้งเตือน', 'ไม่สามารถติดต่อ', 'โทร', 'ข้อความ']
        
        for content in sms_contents:
            if not self.is_thai_text(content):
                continue
                
            content_lower = content.lower()
            
            if any(keyword in content for keyword in banking_keywords):
                categories['banking'].append(content)
            elif any(keyword in content for keyword in verification_keywords):
                categories['verification'].append(content)
            elif any(keyword in content for keyword in promotional_keywords):
                categories['promotional'].append(content)
            elif any(keyword in content for keyword in notification_keywords):
                categories['notification'].append(content)
            else:
                categories['general'].append(content)
        
        return categories
    
    def create_specialized_prompt(self, category: str, messages: List[str]) -> str:
        """Create specialized prompts for different message types"""
        numbered_texts = "\n".join([f"{i+1}. {text}" for i, text in enumerate(messages)])
        
        prompts = {
            'banking': f"""Translate these Thai banking SMS messages to English. 
Maintain financial terminology accuracy and preserve account numbers, amounts, and dates:

{numbered_texts}""",
            
            'verification': f"""Translate these Thai verification/OTP SMS messages to English.
Keep verification codes, numbers, and service names exactly as they are:

{numbered_texts}""",
            
            'promotional': f"""Translate these Thai promotional SMS messages to English.
Maintain marketing tone and preserve brand names, URLs, and promotional codes:

{numbered_texts}""",
            
            'notification': f"""Translate these Thai notification SMS messages to English.
Keep phone numbers, times, and service information accurate:

{numbered_texts}""",
            
            'general': f"""Translate these Thai SMS messages to English.
Maintain casual tone and preserve any special characters or formatting:

{numbered_texts}"""
        }
        
        return prompts.get(category, prompts['general'])
    
    def translate_category_batch(self, category: str, messages: List[str]) -> Dict[str, str]:
        """Translate a batch of messages from the same category"""
        if not messages:
            return {}
        
        try:
            time.sleep(self.delay)
            
            prompt = self.create_specialized_prompt(category, messages)
            
            print(f"🔄 Translating {len(messages)} {category} messages...")
            
            response = self.model.generate_content(prompt)
            translated_lines = response.text.strip().split('\n')
            
            # Parse responses
            translations = {}
            for line in translated_lines:
                if '. ' in line and line.strip():
                    try:
                        num_str, translation = line.split('. ', 1)
                        num = int(num_str) - 1
                        if 0 <= num < len(messages):
                            original = messages[num]
                            translations[original] = translation.strip()
                    except (ValueError, IndexError):
                        continue
            
            success_rate = len(translations) / len(messages) * 100
            print(f"✅ {category}: {len(translations)}/{len(messages)} ({success_rate:.1f}%)")
            
            return translations
            
        except Exception as e:
            print(f"❌ Error translating {category} batch: {e}")
            return {}
    
    def translate_file_hybrid(self, input_file: str) -> bool:
        """Translate file using hybrid strategy"""
        try:
            print(f"\n🎯 Hybrid processing: {input_file}")
            start_time = time.time()
            
            # Read JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract SMS records
            sms_records = data.get('req', {}).get('user', {}).get('mobileSmsRecords', [])
            
            if not sms_records:
                print(f"❌ No SMS records found")
                return False
            
            # Collect Thai contents
            thai_contents = []
            for record in sms_records:
                content = record.get('content', '')
                if content and self.is_thai_text(content):
                    thai_contents.append(content)
            
            if not thai_contents:
                print(f"❌ No Thai SMS content found")
                return False
            
            print(f"📊 Found {len(thai_contents)} Thai SMS messages")
            
            # Categorize messages
            categories = self.categorize_messages(thai_contents)
            
            print(f"\n📋 Message categorization:")
            for category, messages in categories.items():
                if messages:
                    print(f"   {category}: {len(messages)} messages")
            
            # Translate each category with specialized handling
            all_translations = {}
            
            for category, messages in categories.items():
                if not messages:
                    continue
                
                print(f"\n🎯 Processing {category} messages...")
                
                # Process in batches
                for i in range(0, len(messages), self.batch_size):
                    batch = messages[i:i+self.batch_size]
                    batch_num = i // self.batch_size + 1
                    total_batches = (len(messages) - 1) // self.batch_size + 1
                    
                    print(f"📦 {category} batch {batch_num}/{total_batches}")
                    
                    batch_translations = self.translate_category_batch(category, batch)
                    all_translations.update(batch_translations)
            
            # Apply translations
            translated_count = 0
            for record in sms_records:
                content = record.get('content', '')
                if content in all_translations:
                    record['content'] = all_translations[content]
                    translated_count += 1
            
            # Save result
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            base_filename = os.path.basename(input_file)
            output_filename = os.path.join(output_dir, base_filename.replace('.json', '_hybrid_translated.json'))
            
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Performance stats
            total_time = time.time() - start_time
            messages_per_second = len(all_translations) / total_time if total_time > 0 else 0
            success_rate = len(all_translations) / len(thai_contents) * 100
            
            print(f"\n🎉 Hybrid translation completed!")
            print(f"✅ Success rate: {success_rate:.1f}% ({len(all_translations)}/{len(thai_contents)})")
            print(f"⏱️  Total time: {total_time:.1f} seconds")
            print(f"🚀 Speed: {messages_per_second:.1f} messages/second")
            print(f"📄 Output: {output_filename}")
            
            # Show sample translations by category
            print(f"\n📝 Sample translations by category:")
            for category, messages in categories.items():
                if messages and messages[0] in all_translations:
                    original = messages[0][:50] + "..." if len(messages[0]) > 50 else messages[0]
                    translated = all_translations[messages[0]][:50] + "..." if len(all_translations[messages[0]]) > 50 else all_translations[messages[0]]
                    print(f"   {category}: '{original}' → '{translated}'")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    print("🎯 Hybrid Translator - Best of Both Worlds")
    print("=" * 60)
    print("✨ High quality translation with optimized speed")
    print("🎯 Specialized handling for different message types")
    print()
    
    # List files
    json_files = [f for f in os.listdir('.') 
                 if f.endswith('.json') and not f.endswith('_translated.json')]
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print("📋 Available files:")
    for i, file in enumerate(json_files, 1):
        size_kb = os.path.getsize(file) / 1024
        # Estimate time with hybrid approach (faster than basic LLM, slower than Google Translate)
        est_messages = size_kb * 2.5
        est_time_minutes = est_messages / 200  # ~200 messages per minute with hybrid
        
        if est_time_minutes < 1:
            time_str = f"{est_time_minutes*60:.0f}s"
        else:
            time_str = f"{est_time_minutes:.1f}m"
        
        print(f"   {i}. {file} ({size_kb:.0f}KB) - Est: {time_str}")
    
    try:
        choice = int(input(f"\nChoose file (1-{len(json_files)}): ")) - 1
        selected_file = json_files[choice]
        
        translator = HybridTranslator()
        success = translator.translate_file_hybrid(selected_file)
        
        if success:
            print(f"\n🎉 Hybrid translation completed!")
            print(f"💡 Optimized for both quality and speed!")
        else:
            print(f"\n❌ Translation failed")
            
    except (ValueError, IndexError):
        print("❌ Invalid choice")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
